import { Link } from "react-router-dom";

export default function Footer() {
  return (
    <footer style={{
      backgroundColor: '#1e293b',
      color: 'white',
      padding: '40px 20px',
      textAlign: 'center'
    }}>
      <div style={{ maxWidth: '1280px', margin: '0 auto' }}>
        <h3 style={{ marginBottom: '20px', color: '#2563eb' }}>🌍 Elotmani Travel</h3>
        <p style={{ marginBottom: '20px', color: '#94a3b8' }}>
          Votre agence de voyage de confiance pour des expériences inoubliables
        </p>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          gap: '30px',
          marginBottom: '20px',
          flexWrap: 'wrap'
        }}>
          <Link to="/about" style={{ color: '#94a3b8', textDecoration: 'none' }}>À propos</Link>
          <Link to="/tours" style={{ color: '#94a3b8', textDecoration: 'none' }}>Nos Tours</Link>
          <Link to="/contact" style={{ color: '#94a3b8', textDecoration: 'none' }}>Contact</Link>
          <Link to="/gallery" style={{ color: '#94a3b8', textDecoration: 'none' }}>Galerie</Link>
        </div>
        <p style={{ color: '#64748b', fontSize: '14px' }}>
          © 2024 Elotmani Travel. Tous droits réservés.
        </p>
      </div>
    </footer>
  );
}
