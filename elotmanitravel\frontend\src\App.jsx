import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import Layout from './Layouts/Layout'
import Home from './page/Home'
import About from './page/About'
import Tours from './page/Tours'
import Contact from './page/Contact'
import Morocco from './page/Morocco'
import Spain from './page/Spain'
import France from './page/France'
import Gallery from './page/Gallery'
import Reservation from './page/Reservation'
import Login from './page/Login'
import AdminDashboard from './page/AdminDashboard'
import Notfound from './page/Notfound'

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Home />} />
          <Route path="about" element={<About />} />
          <Route path="tours" element={<Tours />} />
          <Route path="destinations" element={<Tours />} />
          <Route path="destinations/morocco" element={<Morocco />} />
          <Route path="destinations/spain" element={<Spain />} />
          <Route path="destinations/france" element={<France />} />
          <Route path="gallery" element={<Gallery />} />
          <Route path="contact" element={<Contact />} />
          <Route path="reservation" element={<Reservation />} />
          <Route path="*" element={<Notfound />} />
        </Route>
        {/* Admin routes - separate layout */}
        <Route path="/admin/login" element={<Login />} />
        <Route path="/admin/dashboard" element={<AdminDashboard />} />
      </Routes>
    </Router>
  )
}

export default App
