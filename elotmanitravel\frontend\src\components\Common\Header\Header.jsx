import { Link } from "react-router-dom";
import { useState } from "react";

export default function Header() {
  const [isDestinationsOpen, setIsDestinationsOpen] = useState(false);

  return (
    <nav style={{
      backgroundColor: 'white',
      borderBottom: '1px solid #e5e7eb',
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
      position: 'sticky',
      top: 0,
      zIndex: 100
    }}>
      <div style={{
        maxWidth: '1280px',
        margin: '0 auto',
        padding: '12px 16px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Link to="/" style={{
          fontSize: '24px',
          fontWeight: 'bold',
          color: '#2563eb',
          textDecoration: 'none'
        }}>
          🌍 Elotmani Travel
        </Link>
        
        <ul style={{
          display: 'flex',
          listStyle: 'none',
          gap: '20px',
          color: '#374151',
          fontWeight: '500',
          margin: 0,
          padding: 0,
          alignItems: 'center'
        }}>
          <li>
            <Link to="/" style={{ 
              color: 'inherit', 
              textDecoration: 'none',
              padding: '10px 16px',
              borderRadius: '6px',
              transition: 'all 0.2s',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              🏠 Home
            </Link>
          </li>
          
          <li>
            <Link to="/about" style={{ 
              color: 'inherit', 
              textDecoration: 'none',
              padding: '10px 16px',
              borderRadius: '6px',
              transition: 'all 0.2s',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              ℹ️ About
            </Link>
          </li>
          
          <li>
            <Link to="/tours" style={{ 
              color: 'inherit', 
              textDecoration: 'none',
              padding: '10px 16px',
              borderRadius: '6px',
              transition: 'all 0.2s',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              🗺️ Tours
            </Link>
          </li>
          
          <li style={{ position: 'relative' }}>
            <button
              onClick={() => setIsDestinationsOpen(!isDestinationsOpen)}
              style={{
                background: 'none',
                border: 'none',
                color: 'inherit',
                padding: '10px 16px',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: 'inherit',
                fontWeight: 'inherit',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                transition: 'all 0.2s'
              }}
            >
              🌍 Destinations {isDestinationsOpen ? '▲' : '▼'}
            </button>
            
            {isDestinationsOpen && (
              <div style={{
                position: 'absolute',
                top: '100%',
                left: 0,
                backgroundColor: 'white',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                minWidth: '200px',
                zIndex: 1000
              }}>
                <Link to="/destinations/morocco" style={{
                  display: 'block',
                  padding: '12px 16px',
                  color: '#374151',
                  textDecoration: 'none',
                  borderBottom: '1px solid #f3f4f6'
                }}>
                  🇲🇦 Morocco
                </Link>
                <Link to="/destinations/spain" style={{
                  display: 'block',
                  padding: '12px 16px',
                  color: '#374151',
                  textDecoration: 'none',
                  borderBottom: '1px solid #f3f4f6'
                }}>
                  🇪🇸 Spain
                </Link>
                <Link to="/destinations/france" style={{
                  display: 'block',
                  padding: '12px 16px',
                  color: '#374151',
                  textDecoration: 'none'
                }}>
                  🇫🇷 France
                </Link>
              </div>
            )}
          </li>
          
          <li>
            <Link to="/gallery" style={{ 
              color: 'inherit', 
              textDecoration: 'none',
              padding: '10px 16px',
              borderRadius: '6px',
              transition: 'all 0.2s',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              📸 Gallery
            </Link>
          </li>
          
          <li>
            <Link to="/contact" style={{ 
              color: 'inherit', 
              textDecoration: 'none',
              padding: '10px 16px',
              borderRadius: '6px',
              transition: 'all 0.2s',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              📞 Contact
            </Link>
          </li>
          
          <li>
            <Link to="/reservation" style={{ 
              backgroundColor: '#2563eb',
              color: 'white',
              textDecoration: 'none',
              padding: '10px 20px',
              borderRadius: '25px',
              transition: 'all 0.2s',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              fontWeight: '600'
            }}>
              📅 Reservation
            </Link>
          </li>
        </ul>
      </div>
    </nav>
  );
}
