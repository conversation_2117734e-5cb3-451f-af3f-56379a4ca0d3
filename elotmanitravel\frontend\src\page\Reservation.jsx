import { useState } from 'react'

export default function Reservation() {
  const [formData, setFormData] = useState({
    nom: '',
    email: '',
    telephone: '',
    destination: '',
    dateDepart: '',
    dateRetour: '',
    nombrePersonnes: 1,
    typeVoyage: '',
    budget: '',
    commentaires: ''
  })

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    alert('Votre demande de réservation a été envoyée! Notre équipe vous contactera dans les 24h.')
    console.log('Données de réservation:', formData)
  }

  return (
    <div style={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      minHeight: '100vh',
      padding: '40px 20px'
    }}>
      <div style={{ maxWidth: '800px', margin: '0 auto' }}>
        <div style={{
          backgroundColor: 'white',
          borderRadius: '20px',
          padding: '40px',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
        }}>
          <div style={{ textAlign: 'center', marginBottom: '40px' }}>
            <h1 style={{
              fontSize: '36px',
              color: '#1e293b',
              marginBottom: '10px'
            }}>
              📅 Réservation de Voyage
            </h1>
            <p style={{ color: '#64748b', fontSize: '18px' }}>
              Planifiez votre voyage de rêve avec nous
            </p>
          </div>

          <form onSubmit={handleSubmit} style={{
            display: 'grid',
            gap: '25px'
          }}>
            {/* Informations personnelles */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
              gap: '20px'
            }}>
              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '8px',
                  fontWeight: '600',
                  color: '#374151'
                }}>
                  Nom complet *
                </label>
                <input
                  type="text"
                  name="nom"
                  value={formData.nom}
                  onChange={handleChange}
                  required
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '16px'
                  }}
                  placeholder="Votre nom complet"
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '8px',
                  fontWeight: '600',
                  color: '#374151'
                }}>
                  Email *
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '16px'
                  }}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div>
              <label style={{
                display: 'block',
                marginBottom: '8px',
                fontWeight: '600',
                color: '#374151'
              }}>
                Téléphone *
              </label>
              <input
                type="tel"
                name="telephone"
                value={formData.telephone}
                onChange={handleChange}
                required
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '16px'
                }}
                placeholder="+212 6 XX XX XX XX"
              />
            </div>

            {/* Détails du voyage */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
              gap: '20px'
            }}>
              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '8px',
                  fontWeight: '600',
                  color: '#374151'
                }}>
                  Destination *
                </label>
                <select
                  name="destination"
                  value={formData.destination}
                  onChange={handleChange}
                  required
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '16px'
                  }}
                >
                  <option value="">Choisir une destination</option>
                  <option value="morocco">🇲🇦 Maroc</option>
                  <option value="spain">🇪🇸 Espagne</option>
                  <option value="france">🇫🇷 France</option>
                  <option value="other">Autre destination</option>
                </select>
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '8px',
                  fontWeight: '600',
                  color: '#374151'
                }}>
                  Type de voyage
                </label>
                <select
                  name="typeVoyage"
                  value={formData.typeVoyage}
                  onChange={handleChange}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '16px'
                  }}
                >
                  <option value="">Type de voyage</option>
                  <option value="famille">Voyage en famille</option>
                  <option value="couple">Voyage en couple</option>
                  <option value="groupe">Voyage en groupe</option>
                  <option value="solo">Voyage solo</option>
                  <option value="affaires">Voyage d'affaires</option>
                </select>
              </div>
            </div>

            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: '20px'
            }}>
              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '8px',
                  fontWeight: '600',
                  color: '#374151'
                }}>
                  Date de départ *
                </label>
                <input
                  type="date"
                  name="dateDepart"
                  value={formData.dateDepart}
                  onChange={handleChange}
                  required
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '16px'
                  }}
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '8px',
                  fontWeight: '600',
                  color: '#374151'
                }}>
                  Date de retour *
                </label>
                <input
                  type="date"
                  name="dateRetour"
                  value={formData.dateRetour}
                  onChange={handleChange}
                  required
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '16px'
                  }}
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '8px',
                  fontWeight: '600',
                  color: '#374151'
                }}>
                  Nombre de personnes
                </label>
                <input
                  type="number"
                  name="nombrePersonnes"
                  value={formData.nombrePersonnes}
                  onChange={handleChange}
                  min="1"
                  max="20"
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '16px'
                  }}
                />
              </div>
            </div>

            <div>
              <label style={{
                display: 'block',
                marginBottom: '8px',
                fontWeight: '600',
                color: '#374151'
              }}>
                Budget approximatif
              </label>
              <select
                name="budget"
                value={formData.budget}
                onChange={handleChange}
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '16px'
                }}
              >
                <option value="">Sélectionner un budget</option>
                <option value="500-1000">500€ - 1000€</option>
                <option value="1000-2000">1000€ - 2000€</option>
                <option value="2000-3000">2000€ - 3000€</option>
                <option value="3000+">Plus de 3000€</option>
              </select>
            </div>

            <div>
              <label style={{
                display: 'block',
                marginBottom: '8px',
                fontWeight: '600',
                color: '#374151'
              }}>
                Commentaires ou demandes spéciales
              </label>
              <textarea
                name="commentaires"
                value={formData.commentaires}
                onChange={handleChange}
                rows="4"
                style={{
                  width: '100%',
                  padding: '12px',
                  border: '2px solid #e2e8f0',
                  borderRadius: '8px',
                  fontSize: '16px',
                  resize: 'vertical'
                }}
                placeholder="Dites-nous en plus sur vos préférences..."
              />
            </div>

            <button
              type="submit"
              style={{
                backgroundColor: '#2563eb',
                color: 'white',
                padding: '16px 32px',
                border: 'none',
                borderRadius: '10px',
                fontSize: '18px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s',
                marginTop: '20px'
              }}
            >
              📅 Envoyer la demande de réservation
            </button>
          </form>

          <div style={{
            marginTop: '30px',
            padding: '20px',
            backgroundColor: '#f8fafc',
            borderRadius: '10px',
            textAlign: 'center'
          }}>
            <h3 style={{ color: '#1e293b', marginBottom: '10px' }}>
              📞 Besoin d'aide ?
            </h3>
            <p style={{ color: '#64748b', marginBottom: '15px' }}>
              Notre équipe est disponible pour vous aider
            </p>
            <div style={{ display: 'flex', justifyContent: 'center', gap: '20px', flexWrap: 'wrap' }}>
              <span style={{ color: '#2563eb' }}>📧 <EMAIL></span>
              <span style={{ color: '#2563eb' }}>📞 +212 6 XX XX XX XX</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

