import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'

export default function AdminDashboard() {
  const navigate = useNavigate()
  const [reservations, setReservations] = useState([
    {
      id: 1,
      nom: '<PERSON>',
      email: '<EMAIL>',
      telephone: '+212 6 12 34 56 78',
      destination: 'morocco',
      dateDepart: '2024-07-15',
      dateRetour: '2024-07-22',
      nombrePersonnes: 2,
      typeVoyage: 'couple',
      budget: '1000-2000',
      status: 'en_attente',
      dateReservation: '2024-06-10'
    },
    {
      id: 2,
      nom: '<PERSON><PERSON>',
      email: '<EMAIL>',
      telephone: '+212 6 98 76 54 32',
      destination: 'spain',
      dateDepart: '2024-08-01',
      dateRetour: '2024-08-08',
      nombrePersonnes: 4,
      typeVoyage: 'famille',
      budget: '2000-3000',
      status: 'confirmee',
      dateReservation: '2024-06-12'
    },
    {
      id: 3,
      nom: '<PERSON>',
      email: '<EMAIL>',
      telephone: '+212 6 11 22 33 44',
      destination: 'france',
      dateDepart: '2024-09-10',
      dateRetour: '2024-09-17',
      nombrePersonnes: 1,
      typeVoyage: 'solo',
      budget: '1000-2000',
      status: 'annulee',
      dateReservation: '2024-06-08'
    }
  ])

  useEffect(() => {
    // Vérifier si l'utilisateur est admin
    const isAdmin = localStorage.getItem('isAdmin')
    if (!isAdmin) {
      navigate('/admin/login')
    }
  }, [navigate])

  const handleLogout = () => {
    localStorage.removeItem('isAdmin')
    navigate('/')
  }

  const updateStatus = (id, newStatus) => {
    setReservations(reservations.map(res => 
      res.id === id ? { ...res, status: newStatus } : res
    ))
  }

  const getStatusColor = (status) => {
    switch(status) {
      case 'en_attente': return '#f59e0b'
      case 'confirmee': return '#10b981'
      case 'annulee': return '#ef4444'
      default: return '#6b7280'
    }
  }

  const getStatusText = (status) => {
    switch(status) {
      case 'en_attente': return 'En attente'
      case 'confirmee': return 'Confirmée'
      case 'annulee': return 'Annulée'
      default: return status
    }
  }

  const getDestinationText = (dest) => {
    switch(dest) {
      case 'morocco': return '🇲🇦 Maroc'
      case 'spain': return '🇪🇸 Espagne'
      case 'france': return '🇫🇷 France'
      default: return dest
    }
  }

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#f8fafc',
      padding: '20px'
    }}>
      {/* Header */}
      <div style={{
        backgroundColor: 'white',
        padding: '20px',
        borderRadius: '10px',
        marginBottom: '30px',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div>
          <h1 style={{ color: '#1e293b', fontSize: '28px', marginBottom: '5px' }}>
            🏢 Dashboard Admin
          </h1>
          <p style={{ color: '#64748b' }}>
            Gestion des réservations - Elotmani Travel
          </p>
        </div>
        <button
          onClick={handleLogout}
          style={{
            backgroundColor: '#ef4444',
            color: 'white',
            padding: '10px 20px',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontWeight: '600'
          }}
        >
          🚪 Déconnexion
        </button>
      </div>

      {/* Stats */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '20px',
        marginBottom: '30px'
      }}>
        <div style={{
          backgroundColor: 'white',
          padding: '20px',
          borderRadius: '10px',
          textAlign: 'center',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
        }}>
          <div style={{ fontSize: '32px', marginBottom: '10px' }}>📊</div>
          <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#2563eb' }}>
            {reservations.length}
          </div>
          <div style={{ color: '#64748b' }}>Total Réservations</div>
        </div>

        <div style={{
          backgroundColor: 'white',
          padding: '20px',
          borderRadius: '10px',
          textAlign: 'center',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
        }}>
          <div style={{ fontSize: '32px', marginBottom: '10px' }}>⏳</div>
          <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#f59e0b' }}>
            {reservations.filter(r => r.status === 'en_attente').length}
          </div>
          <div style={{ color: '#64748b' }}>En Attente</div>
        </div>

        <div style={{
          backgroundColor: 'white',
          padding: '20px',
          borderRadius: '10px',
          textAlign: 'center',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
        }}>
          <div style={{ fontSize: '32px', marginBottom: '10px' }}>✅</div>
          <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#10b981' }}>
            {reservations.filter(r => r.status === 'confirmee').length}
          </div>
          <div style={{ color: '#64748b' }}>Confirmées</div>
        </div>

        <div style={{
          backgroundColor: 'white',
          padding: '20px',
          borderRadius: '10px',
          textAlign: 'center',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
        }}>
          <div style={{ fontSize: '32px', marginBottom: '10px' }}>❌</div>
          <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ef4444' }}>
            {reservations.filter(r => r.status === 'annulee').length}
          </div>
          <div style={{ color: '#64748b' }}>Annulées</div>
        </div>
      </div>

      {/* Table des réservations */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '10px',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }}>
        <div style={{
          padding: '20px',
          borderBottom: '1px solid #e2e8f0'
        }}>
          <h2 style={{ color: '#1e293b', fontSize: '20px' }}>
            📋 Liste des Réservations
          </h2>
        </div>

        <div style={{ overflowX: 'auto' }}>
          <table style={{
            width: '100%',
            borderCollapse: 'collapse'
          }}>
            <thead>
              <tr style={{ backgroundColor: '#f8fafc' }}>
                <th style={{ padding: '12px', textAlign: 'left', color: '#374151', fontWeight: '600' }}>Client</th>
                <th style={{ padding: '12px', textAlign: 'left', color: '#374151', fontWeight: '600' }}>Contact</th>
                <th style={{ padding: '12px', textAlign: 'left', color: '#374151', fontWeight: '600' }}>Destination</th>
                <th style={{ padding: '12px', textAlign: 'left', color: '#374151', fontWeight: '600' }}>Dates</th>
                <th style={{ padding: '12px', textAlign: 'left', color: '#374151', fontWeight: '600' }}>Personnes</th>
                <th style={{ padding: '12px', textAlign: 'left', color: '#374151', fontWeight: '600' }}>Budget</th>
                <th style={{ padding: '12px', textAlign: 'left', color: '#374151', fontWeight: '600' }}>Status</th>
                <th style={{ padding: '12px', textAlign: 'left', color: '#374151', fontWeight: '600' }}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {reservations.map((reservation) => (
                <tr key={reservation.id} style={{ borderBottom: '1px solid #f1f5f9' }}>
                  <td style={{ padding: '12px' }}>
                    <div style={{ fontWeight: '600', color: '#1e293b' }}>{reservation.nom}</div>
                    <div style={{ fontSize: '12px', color: '#64748b' }}>
                      Réservé le {new Date(reservation.dateReservation).toLocaleDateString()}
                    </div>
                  </td>
                  <td style={{ padding: '12px' }}>
                    <div style={{ fontSize: '14px', color: '#374151' }}>{reservation.email}</div>
                    <div style={{ fontSize: '12px', color: '#64748b' }}>{reservation.telephone}</div>
                  </td>
                  <td style={{ padding: '12px' }}>
                    <div style={{ fontWeight: '500' }}>{getDestinationText(reservation.destination)}</div>
                    <div style={{ fontSize: '12px', color: '#64748b' }}>{reservation.typeVoyage}</div>
                  </td>
                  <td style={{ padding: '12px' }}>
                    <div style={{ fontSize: '14px' }}>
                      {new Date(reservation.dateDepart).toLocaleDateString()} - {new Date(reservation.dateRetour).toLocaleDateString()}
                    </div>
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center' }}>
                    <span style={{
                      backgroundColor: '#e0f2fe',
                      color: '#0369a1',
                      padding: '4px 8px',
                      borderRadius: '12px',
                      fontSize: '12px',
                      fontWeight: '600'
                    }}>
                      {reservation.nombrePersonnes}
                    </span>
                  </td>
                  <td style={{ padding: '12px' }}>
                    <div style={{ fontSize: '14px', color: '#374151' }}>{reservation.budget}€</div>
                  </td>
                  <td style={{ padding: '12px' }}>
                    <span style={{
                      backgroundColor: `${getStatusColor(reservation.status)}20`,
                      color: getStatusColor(reservation.status),
                      padding: '4px 8px',
                      borderRadius: '12px',
                      fontSize: '12px',
                      fontWeight: '600'
                    }}>
                      {getStatusText(reservation.status)}
                    </span>
                  </td>
                  <td style={{ padding: '12px' }}>
                    <div style={{ display: 'flex', gap: '5px' }}>
                      <button
                        onClick={() => updateStatus(reservation.id, 'confirmee')}
                        style={{
                          backgroundColor: '#10b981',
                          color: 'white',
                          border: 'none',
                          padding: '4px 8px',
                          borderRadius: '4px',
                          fontSize: '12px',
                          cursor: 'pointer'
                        }}
                      >
                        ✓
                      </button>
                      <button
                        onClick={() => updateStatus(reservation.id, 'en_attente')}
                        style={{
                          backgroundColor: '#f59e0b',
                          color: 'white',
                          border: 'none',
                          padding: '4px 8px',
                          borderRadius: '4px',
                          fontSize: '12px',
                          cursor: 'pointer'
                        }}
                      >
                        ⏳
                      </button>
                      <button
                        onClick={() => updateStatus(reservation.id, 'annulee')}
                        style={{
                          backgroundColor: '#ef4444',
                          color: 'white',
                          border: 'none',
                          padding: '4px 8px',
                          borderRadius: '4px',
                          fontSize: '12px',
                          cursor: 'pointer'
                        }}
                      >
                        ✗
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
