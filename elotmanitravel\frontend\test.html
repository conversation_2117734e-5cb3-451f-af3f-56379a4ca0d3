<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Elotmani Travel - Test React App</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            text-decoration: none;
        }
        
        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
            margin: 0;
            padding: 0;
        }
        
        .nav-links a {
            text-decoration: none;
            color: #374151;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 6px;
            transition: all 0.2s;
        }
        
        .nav-links a:hover {
            background-color: #f3f4f6;
            color: #2563eb;
        }
        
        .login-btn {
            background-color: #2563eb !important;
            color: white !important;
        }
        
        .login-btn:hover {
            background-color: #1d4ed8 !important;
        }
        
        .hero {
            text-align: center;
            padding: 80px 20px;
            color: white;
        }
        
        .hero h1 {
            font-size: 56px;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .hero p {
            font-size: 24px;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .cta-btn {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 30px;
            font-size: 18px;
            text-decoration: none;
            font-weight: 600;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .cta-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .destinations {
            padding: 60px 20px;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .section-title {
            text-align: center;
            font-size: 36px;
            color: white;
            margin-bottom: 50px;
        }
        
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
            cursor: pointer;
        }
        
        .card:hover {
            transform: translateY(-10px);
        }
        
        .card-image {
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 64px;
            background: linear-gradient(45deg, #ff6b6b, #ffa500);
        }
        
        .card-content {
            padding: 25px;
        }
        
        .card h3 {
            font-size: 24px;
            color: #1e293b;
            margin-bottom: 10px;
        }
        
        .card p {
            color: #64748b;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .card-btn {
            background-color: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: background-color 0.2s;
        }
        
        .card-btn:hover {
            background-color: #1d4ed8;
        }
        
        .footer {
            background: rgba(30, 41, 59, 0.9);
            color: white;
            padding: 40px 20px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .footer h3 {
            color: #2563eb;
            margin-bottom: 20px;
        }
        
        .footer-links {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .footer-links a {
            color: #94a3b8;
            text-decoration: none;
        }
        
        .footer-links a:hover {
            color: white;
        }
        
        .page-content {
            display: none;
            padding: 60px 20px;
            background: rgba(255, 255, 255, 0.95);
            margin: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .page-content.active {
            display: block;
        }
        
        .page-content h1 {
            color: #1e293b;
            text-align: center;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="#" class="logo" onclick="showPage('home')">🌍 Elotmani Travel</a>
            <ul class="nav-links">
                <li><a href="#" onclick="showPage('home')">🏠 Accueil</a></li>
                <li><a href="#" onclick="showPage('tours')">🗺️ Tours</a></li>
                <li><a href="#" onclick="showPage('about')">ℹ️ À propos</a></li>
                <li><a href="#" onclick="showPage('contact')">📞 Contact</a></li>
                <li><a href="#" onclick="showPage('gallery')">📸 Galerie</a></li>
                <li><a href="#" onclick="showPage('login')" class="login-btn">🔐 Login</a></li>
            </ul>
        </div>
    </nav>

    <!-- Home Page -->
    <div id="home" class="page-content active">
        <div class="hero">
            <h1>🌍 Bienvenue chez Elotmani Travel</h1>
            <p>Découvrez les plus belles destinations du monde avec notre agence de voyage</p>
            <button class="cta-btn" onclick="showPage('tours')">Découvrir nos destinations ✈️</button>
        </div>

        <div class="destinations">
            <div class="container">
                <h2 class="section-title">Nos Destinations Populaires</h2>
                <div class="cards-grid">
                    <div class="card" onclick="showPage('morocco')">
                        <div class="card-image">🇲🇦</div>
                        <div class="card-content">
                            <h3>Maroc Impérial</h3>
                            <p>Explorez les merveilles du Maroc, de Marrakech aux dunes dorées du Sahara.</p>
                            <button class="card-btn">Découvrir</button>
                        </div>
                    </div>
                    
                    <div class="card" onclick="showPage('spain')">
                        <div class="card-image" style="background: linear-gradient(45deg, #ff9a9e, #fecfef);">🇪🇸</div>
                        <div class="card-content">
                            <h3>Espagne Passionnée</h3>
                            <p>Plongez dans la culture riche de l'Espagne et ses magnifiques plages.</p>
                            <button class="card-btn">Découvrir</button>
                        </div>
                    </div>
                    
                    <div class="card" onclick="showPage('france')">
                        <div class="card-image" style="background: linear-gradient(45deg, #a8edea, #fed6e3);">🇫🇷</div>
                        <div class="card-content">
                            <h3>France Élégante</h3>
                            <p>Visitez Paris la romantique et la Côte d'Azur ensoleillée.</p>
                            <button class="card-btn">Découvrir</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tours Page -->
    <div id="tours" class="page-content">
        <div class="container">
            <h1>🗺️ Nos Circuits et Tours</h1>
            <div class="cards-grid">
                <div class="card">
                    <div class="card-content">
                        <h3>🏛️ Circuit Impérial du Maroc</h3>
                        <p>8 jours / 7 nuits - Découvrez les 4 villes impériales</p>
                        <div style="color: #2563eb; font-size: 24px; font-weight: bold; margin: 15px 0;">1299€ / personne</div>
                        <button class="card-btn">Réserver maintenant</button>
                    </div>
                </div>
                <div class="card">
                    <div class="card-content">
                        <h3>🏜️ Aventure dans le Sahara</h3>
                        <p>5 jours / 4 nuits - Expérience unique dans les dunes</p>
                        <div style="color: #2563eb; font-size: 24px; font-weight: bold; margin: 15px 0;">899€ / personne</div>
                        <button class="card-btn">Réserver maintenant</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Login Page -->
    <div id="login" class="page-content">
        <div class="container" style="max-width: 400px;">
            <h1>🔐 Connexion</h1>
            <form style="display: flex; flex-direction: column; gap: 20px;">
                <input type="email" placeholder="Email" style="padding: 12px; border: 2px solid #e2e8f0; border-radius: 8px; font-size: 16px;">
                <input type="password" placeholder="Mot de passe" style="padding: 12px; border: 2px solid #e2e8f0; border-radius: 8px; font-size: 16px;">
                <button type="submit" style="background-color: #2563eb; color: white; padding: 14px; border: none; border-radius: 8px; font-size: 16px; font-weight: 600; cursor: pointer;">Se connecter</button>
            </form>
        </div>
    </div>

    <!-- Other pages (simplified) -->
    <div id="about" class="page-content">
        <div class="container">
            <h1>ℹ️ À propos d'Elotmani Travel</h1>
            <p style="text-align: center; font-size: 18px; line-height: 1.8; color: #64748b;">
                Elotmani Travel est une agence de voyage spécialisée dans l'organisation de voyages exceptionnels vers les plus belles destinations du monde.
            </p>
        </div>
    </div>

    <div id="contact" class="page-content">
        <div class="container">
            <h1>📞 Contactez-nous</h1>
            <div style="text-align: center;">
                <p>📧 <EMAIL></p>
                <p>📞 +212 6 XX XX XX XX</p>
                <p>📍 Casablanca, Maroc</p>
            </div>
        </div>
    </div>

    <div id="gallery" class="page-content">
        <div class="container">
            <h1>📸 Galerie de nos Destinations</h1>
            <p style="text-align: center; color: #64748b;">Découvrez en images les merveilles que nous vous proposons de visiter</p>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <h3>🌍 Elotmani Travel</h3>
            <p style="color: #94a3b8;">Votre agence de voyage de confiance pour des expériences inoubliables</p>
            <div class="footer-links">
                <a href="#" onclick="showPage('about')">À propos</a>
                <a href="#" onclick="showPage('tours')">Nos Tours</a>
                <a href="#" onclick="showPage('contact')">Contact</a>
                <a href="#" onclick="showPage('gallery')">Galerie</a>
            </div>
            <p style="color: #64748b; font-size: 14px;">© 2024 Elotmani Travel. Tous droits réservés.</p>
        </div>
    </footer>

    <script>
        function showPage(pageId) {
            // Hide all pages
            const pages = document.querySelectorAll('.page-content');
            pages.forEach(page => page.classList.remove('active'));
            
            // Show selected page
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
            }
            
            // Update URL (simulation)
            window.history.pushState({}, '', `#${pageId}`);
        }
        
        // Handle browser back/forward
        window.addEventListener('popstate', function() {
            const hash = window.location.hash.substring(1) || 'home';
            showPage(hash);
        });
        
        // Initialize page based on URL
        document.addEventListener('DOMContentLoaded', function() {
            const hash = window.location.hash.substring(1) || 'home';
            showPage(hash);
        });
        
        // Add some interactivity
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('card-btn')) {
                alert('Merci pour votre intérêt! Notre équipe vous contactera bientôt.');
            }
        });
    </script>
</body>
</html>
