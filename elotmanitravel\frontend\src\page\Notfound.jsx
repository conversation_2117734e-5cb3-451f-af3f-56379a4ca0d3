import { Link } from 'react-router-dom'

export default function Notfound() {
  return (
    <div style={{
      minHeight: '80vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '20px',
      textAlign: 'center'
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '60px 40px',
        borderRadius: '20px',
        boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
        maxWidth: '500px'
      }}>
        <div style={{ fontSize: '120px', marginBottom: '20px' }}>
          🧭
        </div>

        <h1 style={{
          fontSize: '48px',
          color: '#1e293b',
          marginBottom: '20px',
          fontWeight: 'bold'
        }}>
          404
        </h1>

        <h2 style={{
          fontSize: '24px',
          color: '#64748b',
          marginBottom: '20px'
        }}>
          Oops! Page non trouvée
        </h2>

        <p style={{
          color: '#64748b',
          lineHeight: '1.6',
          marginBottom: '40px',
          fontSize: '16px'
        }}>
          Il semble que vous vous soyez égaré dans vos voyages numériques.
          La page que vous cherchez n'existe pas ou a été déplacée.
        </p>

        <div style={{
          display: 'flex',
          gap: '15px',
          justifyContent: 'center',
          flexWrap: 'wrap'
        }}>
          <Link
            to="/"
            style={{
              backgroundColor: '#2563eb',
              color: 'white',
              padding: '12px 24px',
              borderRadius: '8px',
              textDecoration: 'none',
              fontWeight: '600'
            }}
          >
            🏠 Retour à l'accueil
          </Link>

          <Link
            to="/tours"
            style={{
              backgroundColor: 'transparent',
              color: '#2563eb',
              padding: '12px 24px',
              borderRadius: '8px',
              textDecoration: 'none',
              fontWeight: '600',
              border: '2px solid #2563eb'
            }}
          >
            🗺️ Voir nos tours
          </Link>
        </div>
      </div>
    </div>
  )
}

